#!/usr/bin/env python3
"""
Standalone script to compare different activation functions for NNf network extrapolation performance.

This script trains multiple NNf networks with identical hyperparameters but different activation functions,
then evaluates their performance on test data that extends beyond training limits (extrapolation).

Author: Generated for TANN_v4 project
Date: 2025-01-27
"""

import os
import sys
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf

# Add src/utils directory to path for imports
script_dir = Path(__file__).parent
utils_dir = script_dir / 'src' / 'utils'
sys.path.insert(0, str(utils_dir))

# Import required modules
from sub_nn import NNf
from funcs import get_data, pre_process_data, process_data

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

# Set global dtype
tf.keras.backend.set_floatx('float64')
GLOBAL_DTYPE = tf.float64

# Define custom activation function (ELU(x^2))
def custom_act(x):
    return tf.keras.backend.elu(tf.keras.backend.pow(x, 2), alpha=1.0)

# Define custom LeakyReLU function
def leaky_relu(x, alpha=0.2):
    return tf.maximum(alpha * x, x)

# Register custom activation
tf.keras.utils.get_custom_objects().update({'custom_act': tf.keras.layers.Activation(custom_act)})

# Define activation functions to test
activation_functions = {
    'custom_act': 'custom_act',
    'relu': tf.keras.activations.relu,
    'leaky_relu': leaky_relu,
    # 'sigmoid': tf.keras.activations.sigmoid,
    'tanh': tf.keras.activations.tanh,
    'elu': tf.keras.activations.elu,
}

def load_and_prepare_data(train_dataset='10.1', val_dataset='10.2', test_dataset='15.22', E=200000):
    """
    Load and prepare training, validation, and test datasets.

    Parameters:
    - train_dataset: Dataset name for training
    - val_dataset: Dataset name for validation
    - test_dataset: Dataset name for testing (extrapolation)
    - E: Young's modulus

    Returns:
    - Normalized training and validation data, test data, and normalization parameters
    """
    print(f"Loading training data from dataset {train_dataset}...")
    train_raw = get_data(train_dataset, E)
    train_data = pre_process_data(train_raw)

    print(f"Loading validation data from dataset {val_dataset}...")
    val_raw = get_data(val_dataset, E)
    val_data = pre_process_data(val_raw)

    print(f"Loading test data from dataset {test_dataset}...")
    test_raw = get_data(test_dataset, E)
    test_processed = pre_process_data(test_raw)

    # Normalize data using separate training and validation datasets
    n_train_data, n_val_data, norm_params = process_data(train_data, val_data)

    print(f"Training samples: {len(train_data)}")
    print(f"Validation samples: {len(val_data)}")
    print(f"Test samples: {len(test_processed)}")

    return n_train_data, n_val_data, test_processed, norm_params

def train_model_with_activation(activation_name, activation_func, norm_params, train_data, val_data):
    """
    Train an NNf model with specified activation function.

    Parameters:
    - activation_name: Name of activation function
    - activation_func: Activation function
    - norm_params: Normalization parameters
    - train_data: Training data
    - val_data: Validation data

    Returns:
    - Trained model
    """
    print(f"\nTraining NNf model with {activation_name} activation...")

    # Model hyperparameters (from train_nnf.py)
    hidden_dims = [48]
    learning_rate = 3e-4
    n_epochs = 100
    batch_size = 20
    lr_schedule_type = 'exponential'

    # Create model
    model = NNf(norm_params=norm_params, hidden_dims=hidden_dims, activation_func=activation_func)

    # Train model
    try:
        history = NNf.train_model(
            model,
            train_data=train_data,
            val_data=val_data,
            LearningRate=learning_rate,
            nEpochs=n_epochs,
            bSize=batch_size,
            silent_training=True,  # Suppress training output for cleaner logs
            lr_schedule_type=lr_schedule_type
        )
        print(f"Training completed for {activation_name}")
        return model, history
    except Exception as e:
        print(f"Training failed for {activation_name}: {str(e)}")
        return None, None

def run_inference(model, test_data):
    """
    Run inference on test data and extract stress predictions.

    Parameters:
    - model: Trained NNf model
    - test_data: Test data

    Returns:
    - Stress predictions and true values
    """
    if model is None:
        return None, None

    try:
        # Run inference
        results = model.infer(test_data[:, 0:3], solver='network')

        # Extract stress predictions
        stress_pred = np.array(results['summary']['pred_σ'])
        stress_true = test_data[:, 4]  # σ_tdt column

        return stress_pred, stress_true
    except Exception as e:
        print(f"Inference failed: {str(e)}")
        return None, None

def plot_comparison(results_dict, test_data, save_path='activation_comparison.png'):
    """
    Create comparison plot of stress vs strain for different activation functions.

    Parameters:
    - results_dict: Dictionary containing results for each activation function
    - test_data: Test data for extracting strain values
    - save_path: Path to save the plot
    """
    # Calculate cumulative strain from strain increments
    strain_increments = test_data[:, 0]  # Dε column
    strain = np.cumsum(strain_increments)

    # Set up the plot
    plt.figure(figsize=(8, 6.4))
    plt.rcParams["font.family"] = "serif"
    plt.rcParams["font.serif"] = "cmr10"
    plt.rcParams['font.size'] = 12

    # Define colors for different activation functions
    colors = {
        'custom_act': 'blue',
        'relu': 'red',
        'leaky_relu': 'green',
        'sigmoid': 'orange',
        'tanh': 'purple',
        'elu': 'brown'
    }

    # Plot true values first
    if len(results_dict) > 0:
        first_key = list(results_dict.keys())[0]
        if results_dict[first_key]['stress_true'] is not None:
            plt.plot(strain, results_dict[first_key]['stress_true'],
                    color='lightblue', linewidth=2, alpha=0.75, label='True', linestyle='-')

    # Plot predictions for each activation function
    for activation_name, result in results_dict.items():
        if result['stress_pred'] is not None:
            color = colors.get(activation_name, 'gray')
            plt.plot(strain, result['stress_pred'],
                    color=color, marker='o', markersize=2.5, markerfacecolor='white',
                    markeredgecolor=color, markeredgewidth=0.55, linewidth=0.0,
                    alpha=0.9, label=f'{activation_name}')

    # Formatting
    plt.xlabel(r'$\epsilon$ (-)')
    plt.ylabel(r'$\sigma^{t+\Delta t}$ (MPa)')
    plt.title('Activation Function Comparison: Extrapolation Performance')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=11, frameon=True)
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.ticklabel_format(style='sci', axis='x', scilimits=(0, 2))

    # Add minor ticks
    plt.gca().minorticks_on()
    plt.gca().tick_params(which='minor', length=3)
    plt.gca().tick_params(which='major', length=6)

    plt.tight_layout()

    # Save plot
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"\nComparison plot saved as: {save_path}")
    plt.show()

def calculate_metrics(results_dict):
    """
    Calculate and display performance metrics for each activation function.

    Parameters:
    - results_dict: Dictionary containing results for each activation function
    """
    print("\n" + "="*60)
    print("PERFORMANCE METRICS")
    print("="*60)

    for activation_name, result in results_dict.items():
        if result['stress_pred'] is not None and result['stress_true'] is not None:
            pred = result['stress_pred']
            true = result['stress_true']

            # Calculate metrics
            mae = np.mean(np.abs(pred - true))
            mse = np.mean((pred - true)**2)
            rmse = np.sqrt(mse)

            # R² score
            ss_res = np.sum((true - pred)**2)
            ss_tot = np.sum((true - np.mean(true))**2)
            r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

            print(f"\n{activation_name}:")
            print(f"  MAE:  {mae:.6f}")
            print(f"  RMSE: {rmse:.6f}")
            print(f"  R²:   {r2:.6f}")
        else:
            print(f"\n{activation_name}: Training/Inference failed")

def main():
    """
    Main function to run the activation function comparison.
    """
    print("="*60)
    print("ACTIVATION FUNCTION COMPARISON FOR NNf EXTRAPOLATION")
    print("="*60)

    # Load and prepare data
    try:
        n_train_data, n_val_data, test_data, norm_params = load_and_prepare_data()
    except Exception as e:
        print(f"Error loading data: {str(e)}")
        return

    # Dictionary to store results
    results = {}

    # Train models with different activation functions
    for activation_name, activation_func in activation_functions.items():
        model, history = train_model_with_activation(
            activation_name, activation_func, norm_params, n_train_data, n_val_data
        )

        # Run inference
        stress_pred, stress_true = run_inference(model, test_data)

        # Store results
        results[activation_name] = {
            'model': model,
            'history': history,
            'stress_pred': stress_pred,
            'stress_true': stress_true
        }

    # Create comparison plot
    plot_comparison(results, test_data)

    # Calculate and display metrics
    calculate_metrics(results)

    print("\n" + "="*60)
    print("COMPARISON COMPLETED")
    print("="*60)

if __name__ == "__main__":
    main()
