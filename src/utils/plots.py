"""
This module contains plotting utilities for the TANN_v4 project.

Date: 2025-03-04
Author: <PERSON><PERSON><PERSON>
"""
#%% ---- Import Required Libraries ----
import matplotlib.gridspec as gridspec
import matplotlib.pyplot as plt
import numpy as np
import plotly.graph_objects as go
import tensorflow as tf
from funcs import compute_error, compute_r2_score  # Import the functions



from matplotlib.ticker import ScalarFormatter

formatter = ScalarFormatter(useMathText=True)
formatter.set_scientific(True)
formatter.set_powerlimits((0, 0))
class Plotter:
    def __init__ (self):
        plt.rcParams["font.family"] = "serif"
        plt.rc('axes.formatter', use_mathtext=True)
        plt.rcParams["font.serif"] = "cmr10"
        plt.rcParams['font.size']=12

    def plot_data (self, train_data, val_data, test_data):
        '''Plot the data in 3D.'''
        fig = plt.figure()
        ax = fig.add_subplot(projection='3d')
        plt.plot(train_data[:,0], train_data[:,1], train_data[:,3], marker='x', color='blue')
        plt.plot(val_data[:,0], val_data[:,1], val_data[:,3], marker='o', color='red')
        plt.plot(test_data[:,0], test_data[:,1], test_data[:,3], marker='^', color='green')
        plt.show()

    def plot_data_summary (self, test_data, train_data=None, val_data=None, case:int=1):
        """
        Plot a summary of the given datasets, including train, validation and  test sets.

        Parameters:
        - test_data (array-like): Test data.
        - train_data (array-like, optional): Training data. Default is None.
        - val_data (array-like, optional): Validation data. Default is None.
        - case (int, optional): Plotting case. Each case corresponds to a different plot. Default is 1.
            case = 1: Plot strain vs increment for the test set and the training limits.
            case = 2: Plot strain vs increment & stress vs increment for the training sets on a same figure with shared x-axis.
            Also, plot the training limits.
        """

        if test_data is not None:
            strain_test = np.cumsum(test_data[:, 0])
            stress_test = test_data[:, 1]

        if train_data is not None:
            strain_train = np.cumsum(train_data[:, 0])
            stress_train = train_data[:, 1]

        if case==1:         # Plot strain vs increment + training limits
            fig = plt.figure(figsize=(3, 3), dpi=200, tight_layout=True)
            gs = fig.add_gridspec(1, 1)
            ax = fig.add_subplot(gs[0, 0])
            plt.plot(strain_test, color='black', linewidth=1.25)
            ax.set_xlabel('Increment')
            ax.set_ylabel(r'$\epsilon$' + ' (-)')
            ax.axhline(y=strain_train.min(), color='red', linestyle='--', linewidth=0.95, alpha=0.65, label='Training Limit')
            ax.axhline(y=strain_train.max(), color='red', linestyle='--', linewidth=0.95, alpha=0.65)
            self._set_minor_ticks(ax=ax, axis='both', n_minor=3)
            plt.ticklabel_format(style='sci', axis='y', scilimits=(-1, 1), useLocale=True, useMathText=True)
            plt.legend(frameon=False, bbox_to_anchor=(1.07, 1.16), fontsize=11)

        if case==2:         # Plot strain and stress vs increment for training set
            stress_min = np.min(stress_train)
            stress_max = np.max(stress_train)
            strain_train = strain_train*100
            fig, ax1 = plt.subplots(figsize=(4.5, 2), dpi=200, tight_layout=True)
            ax1.plot(strain_train, color='black', linewidth=1.25, label='Strain')
            ax1.set_ylabel(r'$\epsilon (\%)$')
            ax1.set_yticks([strain_train.min(), strain_train.max()])
            ax2 = ax1.twinx()
            ax2.plot(stress_train, color='orange', linewidth=1.25, alpha=0.35, label='Stress')
            ax1.set_xlabel('Increment')
            ax2.set_ylabel(r'$\sigma$' + ' (MPa)')
            ax2.axhline(y=stress_min, color='black', linestyle='--', linewidth=0.5, alpha=0.5)
            ax2.axhline(y=stress_max, color='black', linestyle='--', linewidth=0.5, alpha=0.5)
            ax2.set_yticks([stress_min, 0, stress_max])
            # self._set_minor_ticks(ax=ax1, axis='both', n_minor=3)
            plt.legend(bbox_to_anchor=(0.5, 1.35), loc='upper center', ncol=2, fontsize=10, frameon=False)
            plt.ticklabel_format(style='sci', axis='y', scilimits=(0, 2))

        plt.show()

    def plot_data_scatter (self, train_data, val_data, test_data:None, axis_dict:dict={'x':0, 'y':1, 'z':2}):
        """Plot a 3D scatter plot of the data.

        Parameters:
        - train_data (array-like): Training data.
        - val_data (array-like): Validation data.
        - test_data (array-like): Test data (optional).
        - axis_dict (dict): Dictionary specifying the indices of the features to plot on each axis.
                NOTE: The values and their corresponding titles are: 0: Dε, 1: σ_t, 2: Dζ, 3: F_tdt, 4: σ_tdt, 5: ε_t, 6: ζ_t
        """

        axis_dict = axis_dict
        x_index = axis_dict['x']
        y_index = axis_dict['y']
        z_index = axis_dict['z']

        axis_titles = {0: 'Dε', 1: 'σ_t', 2: 'Dζ', 3: 'F_tdt', 4: 'σ_tdt', 5: 'ε_t', 6: 'ζ_t', 7: 'ε_eq_pl', 8: 'Dε_eq_pl'}

        fig = go.Figure()
        fig.add_trace(go.Scatter3d(x=train_data[:, x_index], y=train_data[:, y_index], z=train_data[:,  z_index],
                                mode='markers', marker=dict(size=4, color='blue', opacity=0.8), name='Training'))
        fig.add_trace(go.Scatter3d(x=val_data[:, x_index], y=val_data[:, y_index], z=val_data[:, z_index],
                                mode='markers', marker=dict(size=4, color='red', opacity=0.8), name='Validation'))
        if test_data is not None:
            fig.add_trace(go.Scatter3d(x=test_data[:, x_index], y=test_data[:, y_index], z=test_data[:, z_index],
                                    mode='markers', marker=dict(size=4, color='green', opacity=0.8), name='Test'))
        fig.update_layout(scene=dict(xaxis_title=axis_titles[x_index], yaxis_title=axis_titles[y_index], zaxis_title=axis_titles[z_index]), width=752, height=750, showlegend=True)

        fig.show()

    def plot_loss (self, history, save_path=None):
        '''Plot the loss curve while training the network.'''
        history_dict = history.history
        loss = history_dict['loss']; val_loss = history_dict['val_loss']
        epochs = range(1, len(loss) + 1)

        fig_loss, ax_loss = plt.subplots(figsize=(6, 5))
        ax_loss.plot(epochs, loss, color='blue', label='train')
        ax_loss.scatter(epochs, val_loss, color='red', marker='o', s=7.5, alpha=.75, label='validation')
        ax_loss.set_title(f'Total loss curve'); ax_loss.set_xlabel('Epoch'); ax_loss.set_ylabel('Loss (MAE)')
        ax_loss.set_xscale('log'); ax_loss.set_yscale('log')
        ax_loss.legend(); ax_loss.grid(True, linestyle='--', alpha=0.5)

        for key in history_dict.keys():
            if 'output' in key and 'val_' not in key:
                val_key = 'val_' + key
                ax_loss.plot(epochs, history_dict[key], label=f'{key}_train')
                ax_loss.scatter(epochs, history_dict[val_key], marker='o', s=3.5, alpha=.5, label=f'{key}_val')
        fig_loss.tight_layout()
        if save_path is not None:
            fig_loss.savefig(fname=f'{save_path}/loss.png', bbox_inches='tight', dpi=500)
        plt.show()

    def plot_prediction(self, y_pred, test_data, title:str, xaxis:str='strain', errors_dict:dict=None, r2_score=None, zoom_in:bool=False, zoom_lim:list=None):
        """
        Plot the predictions of the model along with error distribution and R2 scores.

        Parameters:
        - y_pred (array-like): Predicted values.
        - test_data (array-like): Test data given to the NN model.
        - title (str): Title of the prediction ('F_tdt' or 'σ_tdt').
        - xaxis (str): Type of x-axis ('strain' or 'increment'). Default is 'strain'.
        - errors_dict (dict, optional): Precomputed error arrays and their averages. If None, compute errors within the method.
        - r2_score (float, optional): Precomputed R2 score. If None, compute R2 score within the method.
        - zoom_in (bool, optional): Whether to include a zoomed-in inset plot. Default is False.
        - zoom_lim (list, optional): Limits for the zoomed-in plot [x1, x2, y1, y2]. Required if zoom_in is True.
        """
        from mpl_toolkits.axes_grid1.inset_locator import (inset_axes, mark_inset)

        if 'F_tdt' in title:
            y_true = test_data[:, 3]
            y_label_pred = r'$\mathcal{F}^{t+\Delta t}$'
            error_type = 'mae'
        elif 'σ_tdt' in title:
            y_true = test_data[:, 4]
            y_label_pred = r'$\sigma^{ t+\Delta t}$' + '  (MPa)'
            error_type = 'mae'
        else:
            raise ValueError("Invalid title. Must be 'F_tdt' or 'σ_tdt'.")

        y_label_error = 'Relative Error' if error_type == 'mape' else 'Absolute Error'

        Dε = test_data[:, 0]  # Dε values
        strain = np.cumsum(Dε)

        if errors_dict is None or r2_score is None:
            errors_dict = compute_error(y_pred=y_pred, y_true=y_true)
            r2_score = compute_r2_score(y_pred=y_pred, y_true=y_true)

        avg_err = errors_dict[error_type]['average']
        error_values = errors_dict[error_type]['errors']

        n_inc = y_pred.shape[0]
        # Predictions plot
        # fig = plt.figure(figsize=(5, 7.5))
        fig = plt.figure(figsize=(3, 4.5))
        gs = fig.add_gridspec(2, 1, height_ratios=[1, 0.65])
        ax_pred = fig.add_subplot(gs[0, 0])
        if xaxis.lower() == 'strain':
            ax_pred.plot(strain, y_pred, color='blue', marker='o', markersize=2.5, markerfacecolor='white', markeredgecolor='blue', markeredgewidth=0.55, linewidth=0, alpha=1.0, label='Prediction')
            ax_pred.plot(strain, y_true, color='lightblue', linewidth=2, alpha=0.7, label='True')
            ax_pred.set_xlabel(r'$\epsilon$' + ' (-)')
            ax_pred.ticklabel_format(style='sci', axis='x', scilimits=(0, 2))
        else:
            ax_pred.plot(y_pred, color='blue', marker='o', markersize=2.5, markerfacecolor='white', markeredgecolor='blue', markeredgewidth=0.55, linewidth=0, alpha=1.0, label='Prediction')
            ax_pred.plot(y_true, color='lightblue', linewidth=2, alpha=0.7, label='True')
            ax_pred.set_xlabel('Increment')
        ax_pred.set_ylabel(y_label_pred)
        ax_pred.grid(True, linestyle='--', alpha=0.3)
        # ax_pred.set_xlim((150, 250))
        # ax_pred.legend(fontsize=9)
        # ax_pred.set_title(f'Data set with {n_inc} increments')
        if zoom_in:
            if zoom_lim is None:
                raise ValueError("zoom_lim must be provided when 'zoom_in' is True.")
            x1, x2, y1, y2 = zoom_lim
            axins = inset_axes(ax_pred, 2, 0.67, loc=3, bbox_to_anchor=(0.5, 0.65), bbox_transform=ax_pred.figure.transFigure)
            #NOTE: The inset 'x' axis, either "strain" or "increment" must be explicitly mentioned.
            axins.plot(strain, y_pred, marker='o',  markersize=2.5, markerfacecolor='white', markeredgecolor='blue', markeredgewidth=0.55, linewidth=0, alpha=1.0)
            axins.plot(strain, y_true, color='lightblue', linewidth=2, alpha=0.8)
            axins.set_xlim((x1, x2))
            axins.set_ylim((y1, y2))
            axins.set_xticks([x1, (x1+x2)/2, x2])
            axins.grid(True, linestyle='--', alpha=0.3)
            axins.tick_params(axis='both', which='major', labelsize=12, labelbottom=True)
            axins.ticklabel_format(style='sci', axis='both', scilimits=(-3, 4), useLocale=True, useMathText=True)
            mark_inset(ax_pred, axins, loc1=2, loc2=1, fc="none", ec="0.5", alpha=0.4)

        # Error and R2 plot
        gs_down = gs[1, 0].subgridspec(1, 1)
        ax_err = fig.add_subplot(gs_down[0, 0])
        ax_err.plot(error_values, marker='o', color='red', markersize=2.5, linewidth=0, alpha=0.45)
        ax_err.set_ylabel(f'{y_label_error}', fontsize=12)
        ax_err.set_xlabel('Increment', fontsize=12, loc='center')
        ax_err.text(x=0.03, y=0.95, s=f'{error_type.upper()} = {avg_err:.2e}', transform=ax_err.transAxes, fontsize=9, verticalalignment='top', bbox=dict(boxstyle='round', pad=0.25, edgecolor='gray', facecolor='yellow', alpha=0.6))
        ax_err.tick_params(axis='both', which='major', labelsize=11)
        # ax_err.ticklabel_format(style='scientific', axis='y', scilimits=(-2, -1))
        ax_err.yaxis.set_major_formatter(formatter)

        # ax_err.grid(True, linestyle='-.', alpha=0.3)

        # ax_r2 = fig.add_subplot(gs_down[1, 0])
        # ax_r2.scatter(y_true, y_pred, marker='o', facecolors='white', edgecolors='firebrick', s=18)
        # ax_r2.plot([min(y_true), max(y_true)], [min(y_true), max(y_true)], color='black', linestyle='-.', linewidth=1.8, alpha=0.7)
        # ax_r2.text(x=0.03, y=0.95, s=f'R2 = {r2_score:.4f}', transform=ax_r2.transAxes, fontsize=12, verticalalignment='top', bbox=dict(boxstyle='round, pad=0.25', edgecolor='gray', facecolor='yellow', alpha=0.6))
        # ax_r2.set_xlabel(f'True {y_label_pred}', fontsize=12)
        # ax_r2.set_ylabel(f'Predicted {y_label_pred}', fontsize=12)
        # ax_r2.tick_params(axis='both', which='major', labelsize=11)
        # ax_r2.ticklabel_format(style='sci', axis='both', scilimits=(0, 2))
        # ax_r2.grid(True, linestyle='-.', alpha=0.3)

        fig.tight_layout(rect=[0, 0, 1, 0.99])
        plt.show()

    def plot_nnz_prediction(self, y_pred, test_data, xaxis='strain', errors_dict=None, r2_score=None, zoom_in=False, zoom_lim=None):
        """
        Plot the predictions of the NNz model along with error distribution and R2 scores.

        Parameters:
        - y_pred (array-like): Predicted Dζ values.
        - test_data (array-like): Test data given to the NN model.
        - xaxis (str): Type of x-axis ('strain' or 'increment'). Default is 'strain'.
        - errors_dict (dict, optional): Precomputed error arrays and their averages. If None, compute errors within the method.
        - r2_score (float, optional): Precomputed R2 score. If None, compute R2 score within the method.
        - zoom_in (bool, optional): Whether to include a zoomed-in inset plot. Default is False.
        - zoom_lim (list, optional): Limits for the zoomed-in plot [x1, x2, y1, y2]. Required if zoom_in is True.
        """
        from mpl_toolkits.axes_grid1.inset_locator import (inset_axes, mark_inset)

        y_true = test_data[:, 8]  # True Dζ values from test data
        # y_label_pred = r'$\Delta\zeta$'
        y_label_pred = r'$\Delta \varepsilon^{pl}_{eq}$'
        error_type = 'mae'
        y_label_error = 'Absolute Error'

        Dε = test_data[:, 0]  # Dε values
        strain = np.cumsum(Dε)

        if errors_dict is None or r2_score is None:
            errors_dict = compute_error(y_pred=y_pred, y_true=y_true)
            r2_score = compute_r2_score(y_pred=y_pred, y_true=y_true)

        avg_err = errors_dict[error_type]['average']
        error_values = errors_dict[error_type]['errors']

        n_inc = y_pred.shape[0]

        # Predictions plot
        fig = plt.figure(figsize=(3, 4.5))
        gs = fig.add_gridspec(2, 1, height_ratios=[1, 0.65])
        ax_pred = fig.add_subplot(gs[0, 0])

        if xaxis.lower() == 'strain':
            ax_pred.plot(strain[y_true==0], y_pred[y_true==0], color='blue', marker='o', markersize=2.5, markerfacecolor='white',
                        markeredgecolor='blue', markeredgewidth=0.55, linewidth=0, alpha=1.0, label='Prediction')
            ax_pred.plot(strain[y_true==0], y_true[y_true==0], color='lightblue', linewidth=2, alpha=0.7, label='True')
            ax_pred.set_xlabel(r'$\epsilon$' + ' (-)')
            ax_pred.ticklabel_format(style='sci', axis='x', scilimits=(0, 2))
            ax_pred.set_ylim((-1e-6, 1e-6))
        else:
            ax_pred.plot(y_pred, color='blue', marker='o', markersize=2.5, markerfacecolor='white',
                        markeredgecolor='blue', markeredgewidth=0.55, linewidth=0, alpha=1.0, label='Prediction')
            ax_pred.plot(y_true, color='lightblue', linewidth=2, alpha=0.7, label='True')
            ax_pred.set_xlabel('Increment')

        ax_pred.set_ylabel(y_label_pred)
        ax_pred.ticklabel_format(style='sci', axis='y', scilimits=(0, 2))
        ax_pred.grid(True, linestyle='--', alpha=0.3)
        # ax_pred.legend()
        # ax_pred.set_title(f'Data set with {n_inc} increments')

        if zoom_in:
            if zoom_lim is None:
                raise ValueError("zoom_lim must be provided when 'zoom_in' is True.")
            x1, x2, y1, y2 = zoom_lim
            axins = inset_axes(ax_pred, 2, 0.67, loc=3, bbox_to_anchor=(0.5, 0.65), bbox_transform=ax_pred.figure.transFigure)

            if xaxis.lower() == 'strain':
                axins.plot(strain, y_pred, marker='o', markersize=2.5, markerfacecolor='white',
                          markeredgecolor='blue', markeredgewidth=0.55, linewidth=0, alpha=1.0)
                axins.plot(strain, y_true, color='lightblue', linewidth=2, alpha=0.8)
            else:
                axins.plot(y_pred, marker='o', markersize=2.5, markerfacecolor='white',
                          markeredgecolor='blue', markeredgewidth=0.55, linewidth=0, alpha=1.0)
                axins.plot(y_true, color='lightblue', linewidth=2, alpha=0.8)

            axins.set_xlim((x1, x2))
            axins.set_ylim((y1, y2))
            axins.set_xticks([x1, (x1+x2)/2, x2])
            axins.grid(True, linestyle='--', alpha=0.3)
            axins.tick_params(axis='both', which='major', labelsize=12, labelbottom=True)
            axins.ticklabel_format(style='sci', axis='both', scilimits=(-3, 4), useLocale=True, useMathText=True)
            mark_inset(ax_pred, axins, loc1=2, loc2=1, fc="none", ec="0.5", alpha=0.4)

        # Error and R2 plot
        gs_down = gs[1, 0].subgridspec(1, 1)
        ax_err = fig.add_subplot(gs_down[0, 0])
        ax_err.plot(error_values, marker='s', color='darkred', markersize=2.5, linewidth=0, alpha=0.45)
        ax_err.set_ylabel(f'{y_label_error}', fontsize=12)
        ax_err.set_xlabel('Increment', fontsize=12, loc='center')
        ax_err.text(x=0.03, y=0.95, s=f'{error_type.upper()} = {avg_err:.2e}', transform=ax_err.transAxes,
                   fontsize=12, verticalalignment='top',
                   bbox=dict(boxstyle='round, pad=0.25', edgecolor='gray', facecolor='yellow', alpha=0.6))
        ax_err.tick_params(axis='both', which='major', labelsize=11)
        ax_err.ticklabel_format(style='sci', axis='y', scilimits=(0, 2))
        ax_err.grid(True, linestyle='-.', alpha=0.3)

        fig.tight_layout(rect=[0, 0, 1, 0.99])
        plt.show()

    def plot_data_histograms_grid(self, train_data, val_data, test_data=None,
                                 columns_to_plot:list=None, custom_feature_names=None,
                                 bins=30, figsize=(8.5, 5), use_log_scale_yaxis=True, save_path=None):
        """
        Plot histograms of selected data columns in a grid layout.

        Parameters:
        - train_data (array-like): Training data with shape [n_samples, n_features]
        - val_data (array-like): Validation data with shape [n_samples, n_features]
        - test_data (array-like, optional): Test data with shape [n_samples, n_features]
        - columns_to_plot (list, optional): Indices of columns to plot. If None, plots columns 0-4.
        - custom_feature_names (dict, optional): Custom names for specific features {column_index: name}.
                                               Overrides default names.
        - bins (int, optional): Number of bins for histograms. Default is 30.
        - figsize (tuple, optional): Figure size (width, height). Default is (8.5, 5).
        - use_log_scale_yaxis (bool, optional): Whether to use log scale for y-axis. Default is True.
        - save_path (str, optional): Path to save the figure. If None, figure is not saved.
        """
        # Define a standard mapping of column indices to feature names
        default_feature_names = {
            0: r'$\Delta\varepsilon$',                  # Dε (strain increment)
            1: r'$\sigma_t$',                           # σ_t (stress at time t)
            2: r'$\Delta\zeta$',                        # Dζ (state variable increment)
            3: r'$\mathcal{F}^{ t+\Delta t}$',          # F_tdt (free energy at t+dt)
            4: r'$\sigma^{ t+\Delta t}$',               # σ_tdt (stress at t+dt)
            5: r'$\varepsilon_t$',                      # ε_t (strain at time t)
            6: r'$\zeta_t$',                            # ζ_t (accumulated state variable)
            7: r'$\varepsilon_{eq, t}^{pl}$',           # ε_eq_pl_t (accumulated equivalent plastic strain)
            8: r'$\Delta\varepsilon_{eq}^{pl}$'         # Dε_eq_pl (equivalent plastic strain increment)
        }

        # If custom feature names are provided, update the default mapping
        if custom_feature_names:
            default_feature_names.update(custom_feature_names)

        if columns_to_plot is None:
            columns_to_plot = [0, 1, 2, 3, 4]  # Default to standard NNf columns

        n_features = len(columns_to_plot)

        # Calculate grid dimensions
        n_cols = min(3, n_features)  # Maximum 3 columns
        n_rows = (n_features + n_cols - 1) // n_cols  # Ceiling division

        fig = plt.figure(figsize=figsize, dpi=200)
        gs = gridspec.GridSpec(n_rows, n_cols, figure=fig)

        for i, col_idx in enumerate(columns_to_plot):
            ax = fig.add_subplot(gs[i // n_cols, i % n_cols])

            # Get feature name from the dictionary
            feature_name = default_feature_names.get(col_idx, f'Feature {col_idx}')

            # Extract data for the current column
            train_feature = train_data[:, col_idx]
            val_feature = val_data[:, col_idx]

            # Determine common range for all datasets
            min_value = min(np.min(train_feature), np.min(val_feature))
            max_value = max(np.max(train_feature), np.max(val_feature))
            if test_data is not None:
                test_feature = test_data[:, col_idx]
                min_value = min(min_value, np.min(test_feature))
                max_value = max(max_value, np.max(test_feature))

            # Add a small buffer to the range
            range_buffer = (max_value - min_value) * 0.05 if max_value > min_value else abs(min_value) * 0.05
            hist_range = (min_value - range_buffer, max_value + range_buffer)

            ax.hist(train_feature, bins=bins, histtype='step', alpha=0.8, label='Training', range=hist_range, color='blue')
            ax.hist(val_feature, bins=bins, histtype='stepfilled', alpha=0.5, label='Validation', range=hist_range, color='crimson')
            if test_data is not None:
                ax.hist(test_feature, bins=bins, histtype='stepfilled', edgecolor='black', alpha=0.65, label='Test', range=hist_range, color='darkslategrey')
            ax.set_xlabel(feature_name)
            ax.set_ylabel(r'$\mathit{N}_{samples}$')
            if use_log_scale_yaxis: ax.set_yscale('log')
            self._set_minor_ticks(ax=ax, axis='x' if use_log_scale_yaxis else 'both', n_minor=3)
            ax.tick_params(axis='both', which='minor', length=1.3, width=0.8, labelsize=0)

            # Only add legend to one subplot to save space
            if i == 3:
                ax.legend(loc='upper right', fontsize=9, frameon=False)

        plt.tight_layout()

        if save_path is not None:
            plt.savefig(f'{save_path}/data_histograms_grid.png', bbox_inches='tight', dpi=300)

        plt.show()

    def plot_feature_correlations(self, train_data, val_data=None, test_data=None,
                             feature_names=None, bins=50, figsize=(15, 15), save_path=None):
        """
        Plot 2D histograms showing correlations between pairs of features.

        Parameters:
        - train_data (array-like): Training data with shape [n_samples, n_features]
        - val_data (array-like, optional): Validation data. If provided, will be plotted separately.
        - test_data (array-like, optional): Test data. If provided, will be plotted separately.
        - feature_names (list, optional): Names of features. If None, uses default names.
        - bins (int, optional): Number of bins for histograms. Default is 50.
        - figsize (tuple, optional): Figure size (width, height). Default is (15, 15).
        - save_path (str, optional): Path to save the figure. If None, figure is not saved.
        """
        # Determine the number of features
        n_features = train_data.shape[1]

        # Default feature names if not provided
        if feature_names is None:
            # Assuming standard format: [Dε, σ_t, Dζ, F_true, σ_true]
            feature_names = ['Dε', 'σ_t', 'Dζ', 'F_tdt', 'σ_tdt'][:n_features]

        # Create a figure with a grid of subplots
        fig, axes = plt.subplots(n_features, n_features, figsize=figsize)

        # If there's only one feature, axes will not be a 2D array
        if n_features == 1:
            axes = np.array([[axes]])

        # Plot 2D histograms for each pair of features
        for i in range(n_features):
            for j in range(n_features):
                ax = axes[i, j]

                if i == j:  # Diagonal: plot 1D histogram
                    ax.hist(train_data[:, i], bins=bins, alpha=0.7, color='blue')
                    ax.set_title(feature_names[i])
                    ax.set_xlabel(feature_names[i])
                    if i == 0:
                        ax.set_ylabel('Frequency')
                else:  # Off-diagonal: plot 2D histogram
                    h = ax.hist2d(train_data[:, j], train_data[:, i], bins=bins, cmap='viridis')

                    # Add colorbar only to the rightmost plots
                    if j == n_features - 1:
                        plt.colorbar(h[3], ax=ax)

                    if i == n_features - 1:
                        ax.set_xlabel(feature_names[j])
                    if j == 0:
                        ax.set_ylabel(feature_names[i])

        # Add a title
        plt.suptitle('Feature Correlations (Training Data)', fontsize=16)

        # Adjust layout
        plt.tight_layout(rect=[0, 0, 1, 0.97])

        # Save figure if path is provided
        if save_path is not None:
            plt.savefig(f'{save_path}/feature_correlations.png', bbox_inches='tight', dpi=300)

        plt.show()
        return fig, axes

    def plot_learning_rate_schedules(self, initial_lr=3e-4, num_epochs=3500):
        """
        Plot different learning rate schedules for comparison.
        Compares constant, exponential decay, and cosine decay with restarts.

        Parameters:
        - initial_lr: Initial learning rate
        - num_epochs: Total number of epochs
        """
        import os
        os.makedirs('logs', exist_ok=True)

        epochs = np.arange(1, num_epochs + 1)

        # Constant learning rate
        constant_lr = np.ones_like(epochs) * initial_lr

        # Exponential decay
        decay_steps = num_epochs // 2
        exp_decay = tf.keras.optimizers.schedules.ExponentialDecay(
            initial_learning_rate=initial_lr,
            decay_steps=decay_steps,
            decay_rate=0.5,
            staircase=False
        )
        exp_decay_values = [exp_decay(e).numpy() for e in epochs]

        # Cosine decay with restarts
        first_decay_steps = num_epochs // 5
        cosine_decay = tf.keras.optimizers.schedules.CosineDecayRestarts(
            initial_learning_rate=initial_lr,
            first_decay_steps=first_decay_steps,
            t_mul=2.0,
            m_mul=0.9,
            alpha=1e-5
        )
        cosine_decay_values = [cosine_decay(e).numpy() for e in epochs]

        plt.figure(figsize=(8, 4))

        plt.subplot(1, 2, 1)
        plt.plot(epochs, constant_lr, color='darkorange', label='Constant')
        plt.plot(epochs, exp_decay_values, color='steelblue', label='Exponential Decay')
        plt.plot(epochs, cosine_decay_values, 'g-', label='Cosine Decay with Restarts')
        plt.xlabel('Epoch')
        plt.title('Learning Rate Schedules', fontsize=12)
        self._set_minor_ticks(ax=plt.gca(), axis='both', n_minor=3)
        plt.legend(fontsize=8, loc='lower right'); plt.ticklabel_format(style='sci', axis='y', scilimits=(0, 2))
        plt.grid(True, linestyle='--', alpha=0.25)

        plt.subplot(1, 2, 2)
        plt.semilogy(epochs, constant_lr, 'b-', label='Constant')
        plt.semilogy(epochs, exp_decay_values, 'r-', label='Exponential Decay')
        plt.semilogy(epochs, cosine_decay_values, 'g-', label='Cosine Decay with Restarts')
        plt.xlabel('Epoch')
        plt.title('Learning Rate Schedules (Log Scale)', fontsize=12)
        self._set_minor_ticks(ax=plt.gca(), axis='x', n_minor=3)
        plt.legend(fontsize=8, loc='lower right')
        plt.grid(True, linestyle='--', alpha=0.25)

        plt.tight_layout()
        plt.savefig('logs/learning_rate_schedules.png', dpi=300, bbox_inches='tight')
        plt.show()

        print(f"Learning rate schedules visualization saved to logs/learning_rate_schedules.png")

#%% Static Methods
    @staticmethod
    def _set_minor_ticks(ax, axis='both', n_minor=4):
        """
        Set minor ticks automatically based on major tick spacing.

        Parameters:
        - ax: matplotlib axis object
        - axis: 'x', 'y', or 'both'
        - n_minor: number of minor ticks between major ticks
        """
        if axis in ['x', 'both']:
            major_ticks = ax.get_xticks()
            if len(major_ticks) > 1:
                major_spacing = major_ticks[1] - major_ticks[0]
                minor_spacing = major_spacing / (n_minor + 1)
                ax.xaxis.set_minor_locator(plt.MultipleLocator(minor_spacing))

        if axis in ['y', 'both']:
            major_ticks = ax.get_yticks()
            if len(major_ticks) > 1:
                major_spacing = major_ticks[1] - major_ticks[0]
                minor_spacing = major_spacing / (n_minor + 1)
                ax.yaxis.set_minor_locator(plt.MultipleLocator(minor_spacing))