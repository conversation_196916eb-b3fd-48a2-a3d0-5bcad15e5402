"""
This module contains the main model TANN class for the TANN_v4 project.
It includes the model architecture, training, saving, and loading functionalities.

Date: 2025-03-04
Author: <PERSON><PERSON><PERSON>
"""
#%% ---- Import Required Libraries ----
import inspect
import json
import os
from datetime import datetime

import numpy as np
import tensorflow as tf
from tann_debug import NNfDebugClass

#%% ---- Random Seed ----
seed=42
tf.random.set_seed(seed)

# %% Set the default floating-point precision for Keras backend operations.
tf.keras.backend.set_floatx('float64')
GLOBAL_DTYPE = tf.float64

# %% Activation Functions
# Define the customized activation function: ELU(x^2)
def custom_act (x):
    return tf.keras.backend.elu(tf.keras.backend.pow(x, 2), alpha=1.0)

# Define a custom LeakyReLU function if needed
def leaky_relu(x, alpha=0.2):
    """Custom implementation of leaky ReLU activation function."""
    return tf.maximum(alpha * x, x)

# Register the ELU(x^2) activation function.
tf.keras.utils.get_custom_objects().update({'custom_act' : tf.keras.layers.Activation(custom_act)})

activation_dict = {
    'custom_act': 'custom_act',
    'relu': tf.keras.activations.relu,
    'leaky_relu': leaky_relu,
    'sigmoid': tf.keras.activations.sigmoid,
    'tanh': tf.keras.activations.tanh,
    'elu': tf.keras.activations.elu,
}

#%% ---- NNf Class ----
class NNf (tf.keras.Model, NNfDebugClass):
    def __init__ (self, norm_params, hidden_dims, activation_func='custom_act'):
        """
        Configure the layers of the model based on the given number of hidden layers (excl. output layer) and nodes.

        Parameters:
        - hidden_dims (list): List of integers representing the number of nodes in each hidden layer.
        - activation_func (str): Activation function for the hidden layers.
        """
        super().__init__()
        self.num_inputs = 3
        self.num_outputs = 1
        self.activation_func = activation_func
        self.norm_params = norm_params

        self.setup_logger()

        self.hidden_layers =[]
        for i, h_dim in enumerate(hidden_dims):
            self.hidden_layers.append(tf.keras.layers.Dense(
                h_dim,
                activation=self.activation_func,
                name=f'NNf_Hlayer{i+1}',
            ))
        self.out_layer = tf.keras.layers.Dense(self.num_outputs, name='NNf_OutLayer')

        self.build((None, self.num_inputs))

    def call (self, inputs):
        # ----------
        inp_Dε = tf.slice(inputs, [0, 0], [-1, 1])
        inp_σ_t = tf.slice(inputs, [0, 1], [-1, 1])
        inp_Dζ = tf.slice(inputs, [0, 2], [-1, 1])
        n_concat = tf.concat([inp_Dε, inp_σ_t, inp_Dζ], axis=1, name='inputs_concat_of_NNf')
        for layer in self.hidden_layers:
            n_concat = layer(n_concat)
        n_F_tdt = self.out_layer(n_concat)

        return n_F_tdt

    def infer(self, input_test, solver='network'):
        """Perform inference (iterative prediction) on a sequence of inputs.

        Parameters:
        - input_test: Input data with shape (num_samples, num_features)
        - solver: Method to compute F_tdt, either 'network' (using neural network) or 'analytic' (using analytical equation)

        Returns:
        - result_dict: Dictionary containing step-by-step results and summary statistics
        """
        Dε_full = tf.slice(input_test, [0, 0], [-1, 1])
        σ_t_full = tf.slice(input_test, [0, 1], [-1, 1])
        Dζ_full = tf.slice(input_test, [0, 2], [-1, 1])
        n_Dε_full = self._Normalize(Dε_full, self.norm_params[0])
        n_σ_t_full = self._Normalize(σ_t_full, self.norm_params[1])
        n_Dζ_full = self._Normalize(Dζ_full, self.norm_params[2])

        n_σ_t = n_σ_t_full[0, None]
        del σ_t_full, n_σ_t_full            # To make sure they are not used later in the method.
        num_test = len(n_Dε_full)

        pred_F, pred_σ, F_errors, σ_errors, F_errors_percent, σ_errors_percent = \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64)

        step_results = []

        for i in range(num_test):
            input_i = tf.concat([n_Dε_full[i, None], n_σ_t, n_Dζ_full[i, None]], axis=1)

            if solver.lower() == 'network':
                n_F_pred = self.call(input_i)
                n_σ_pred = self.get_stress(input_i, solver='network')
            # elif solver.lower() == 'analytic':
            #     n_F_pred = self.get_analytic_F(input_i)
            #     n_σ_pred = self.get_stress(input_i, solver='analytic')
            else:
                raise ValueError(f"Unknown solver: {solver}. Use 'network' or 'analytic'.")

            F_pred = self._UnNormalize(n_F_pred, self.norm_params[3]).numpy()[0, 0]
            σ_pred = self._UnNormalize(n_σ_pred, self.norm_params[1]).numpy()[0, 0]

            pred_F[i, 0] = F_pred
            pred_σ[i, 0] = σ_pred

            # Calculate errors if true values are available (input has at least 5 columns)
            if input_test.shape[1] >= 5:
                true_F = input_test[i, 3]
                true_σ = input_test[i, 4]

                F_error = F_pred - true_F
                σ_error = σ_pred - true_σ

                F_error_percent = (F_error / true_F) * 100 if true_F != 0 else float('inf')
                σ_error_percent = (σ_error / true_σ) * 100 if true_σ != 0 else float('inf')

                F_errors[i, 0] = F_error
                σ_errors[i, 0] = σ_error
                F_errors_percent[i, 0] = F_error_percent
                σ_errors_percent[i, 0] = σ_error_percent
            else:
                true_F = None
                true_σ = None
                F_error = None
                σ_error = None
                F_error_percent = None
                σ_error_percent = None

            step_result = {
                'step': i,
                'solver': solver,
                'inputs': {
                    'Dε': Dε_full.numpy()[i, 0],
                    'σ_t': self._UnNormalize(n_σ_t, self.norm_params[1]).numpy()[0, 0],
                    'Dζ': Dζ_full.numpy()[i, 0]
                },
                'predictions': {'F': F_pred, 'σ': σ_pred}
            }

            # Add true values and errors if available
            if input_test.shape[1] >= 5:
                step_result['true_values'] = {'F': true_F, 'σ': true_σ}
                step_result['errors'] = {
                    'F': F_error,
                    'F_percent': F_error_percent,
                    'σ': σ_error,
                    'σ_percent': σ_error_percent
                }

            step_results.append(step_result)

            # Update stress for next step
            if i < num_test - 1:  # Only update if not the last sample
                # if true_data and input_test.shape[1] >= 5:
                #     n_σ_t = n_σ_t_full[i+1, None]
                # else:
                #     n_σ_t = n_σ_pred
                n_σ_t = n_σ_pred

        summary = {
            'pred_F': pred_F.flatten().tolist(),
            'pred_σ': pred_σ.flatten().tolist(),
        }

        # Add error statistics if target values were available
        if input_test.shape[1] >= 5:
            summary.update({
                'true_F': input_test[:, 3].tolist(),
                'true_σ': input_test[:, 4].tolist(),
                'F_errors': F_errors.flatten().tolist(),
                'σ_errors': σ_errors.flatten().tolist(),
                'F_errors_percent': F_errors_percent.flatten().tolist(),
                'σ_errors_percent': σ_errors_percent.flatten().tolist(),
                'avg_F_error': float(np.mean(np.abs(F_errors))),
                'avg_σ_error': float(np.mean(np.abs(σ_errors))),
                'avg_F_error_percent': float(np.mean(np.abs(F_errors_percent))),
                'avg_σ_error_percent': float(np.mean(np.abs(σ_errors_percent))),
                'max_F_error': float(np.max(np.abs(F_errors))),
                'max_σ_error': float(np.max(np.abs(σ_errors))),
                'max_F_error_percent': float(np.max(np.abs(F_errors_percent))),
                'max_σ_error_percent': float(np.max(np.abs(σ_errors_percent)))
            })

        result_dict = {
            'step_results': step_results,
            'summary': summary,
            'solver': solver,
            'num_samples': num_test
        }

        return result_dict

    def get_analytic_F(self, input_i):
        """Compute the analytical free energy F_tdt.

        Parameters:
        - input_i: A tensor of shape (batch_size, 3), containing the normalized inputs [n_Dε, n_σ_t, n_Dζ]

        Returns:
        - n_F_tdt_analytic: The normalized analytical free energy
        """
        n_Dε = tf.slice(input_i, [0, 0], [-1, 1])
        n_σ_t = tf.slice(input_i, [0, 1], [-1, 1])
        n_Dζ = tf.slice(input_i, [0, 2], [-1, 1])

        # Un-normalize the inputs
        Dε = self._UnNormalize(n_Dε, self.norm_params[0])
        σ_t = self._UnNormalize(n_σ_t, self.norm_params[1])
        Dζ = self._UnNormalize(n_Dζ, self.norm_params[2])
        E = 200000

        F_tdt_analytic = 0.5 * (σ_t * (E ** -1) * σ_t) + 0.5 * E * ((Dε - Dζ) * (Dε - Dζ + (2 * σ_t) / E))
        n_F_tdt_analytic = self._Normalize(F_tdt_analytic, self.norm_params[3])

        return n_F_tdt_analytic

    def get_stress (self, input_i, solver:str='network'):
        """Compute the derivative of F_tdt with respect to Dε using the central difference method.

        Parameters:
        - input_i: A tensor of shape (batch_size, 3), containing the normalized inputs [n_Dε, n_σ_t, n_Dζ]
        - solver: Method to compute F_tdt, either 'network' (using neural network) or 'analytic' (using analytical equation)

        Returns:
        - n_σ_tdt: The normalized stress at t+dt
        """
        n_Dε = tf.slice(input_i, [0, 0], [-1, 1])
        n_σ_t = tf.slice(input_i, [0, 1], [-1, 1])
        n_Dζ = tf.slice(input_i, [0, 2], [-1, 1])

        Dε = self._UnNormalize(n_Dε, self.norm_params[0])
        local_delta = tf.maximum(1e-16, 1e-6 * tf.abs(Dε))
        Dε_plus = tf.add(Dε, local_delta)
        Dε_minus = tf.add(Dε, -local_delta)
        n_Dε_plus = self._Normalize(Dε_plus, self.norm_params[0])
        n_Dε_minus = self._Normalize(Dε_minus, self.norm_params[0])

        if solver.lower() == 'network':
            n_F_plus = self.call(tf.concat([n_Dε_plus, n_σ_t, n_Dζ], axis=1))
            n_F_minus = self.call(tf.concat([n_Dε_minus, n_σ_t, n_Dζ], axis=1))
        # elif solver.lower() == 'analytic':
        #     n_F_plus = self.get_analytic_F(tf.concat([n_Dε_plus, n_σ_t, n_Dζ], axis=1))
        #     n_F_minus = self.get_analytic_F(tf.concat([n_Dε_minus, n_σ_t, n_Dζ], axis=1))

        F_plus = self._UnNormalize(n_F_plus, self.norm_params[3])
        F_minus = self._UnNormalize(n_F_minus, self.norm_params[3])

        σ_tdt = tf.divide(tf.subtract(F_plus, F_minus), 2*local_delta)
        n_σ_tdt = self._Normalize(σ_tdt, self.norm_params[1])

        return n_σ_tdt

    @classmethod
    def train_model(cls, model_instance, train_data, val_data, LearningRate, nEpochs, bSize, silent_training=False, early_stopping=None, lr_schedule_type='exponential'):
        """
        Train the model with learning rate scheduling.

        Parameters:
        - model_instance: Instance of NNf_TwoInputs to train
        - train_data: Training data with shape (num_samples, num_features)
        - val_data: Validation data with shape (num_samples, num_features)
        - LearningRate: Initial learning rate for the optimizer
        - nEpochs: Number of epochs to train
        - bSize: Batch size
        - silent_training: If True, suppress training output
        - early_stopping: Early stopping callback
        - lr_schedule_type: Type of learning rate schedule to use ('exponential', 'cosine', or 'constant')

        Returns:
        - history: Training history
        """
        silent_verbose = 0 if silent_training else 1
        if early_stopping is None:
            early_stopping = tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=500, restore_best_weights=True)

        callbacks = [early_stopping]

        if lr_schedule_type == 'exponential':
            # Exponential decay: decay to 1% of initial LR over the training period
            decay_steps = nEpochs // 2  # Decay significantly over 1/2 of training
            decay_rate = 0.75   # Decay the learning rate every decay_steps
            lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
                initial_learning_rate=LearningRate,
                decay_steps=decay_steps,
                decay_rate=decay_rate,
                staircase=False  # Smooth decay
            )
            print(f"Using exponential learning rate decay: initial={LearningRate}, decay_steps={decay_steps}, decay_rate={decay_rate}")

        elif lr_schedule_type == 'cosine':
            # Cosine decay with restarts
            first_decay_steps = nEpochs // 5  # First cycle length
            lr_schedule = tf.keras.optimizers.schedules.CosineDecayRestarts(
                initial_learning_rate=LearningRate,
                first_decay_steps=first_decay_steps,
                t_mul=2.0,  # Each cycle gets longer
                m_mul=0.9,  # Each restart has lower max learning rate
                alpha=1e-5   # Minimum learning rate
            )
            print(f"Using cosine decay with restarts: initial={LearningRate}, first_decay_steps={first_decay_steps}")

        else:  # 'constant' or any other value
            lr_schedule = LearningRate
            print(f"Using constant learning rate: {LearningRate}")

        # Add LearningRateScheduler callback to log the learning rate
        class LRLogger(tf.keras.callbacks.Callback):
            def on_epoch_end(self, epoch, logs=None):
                if not silent_training and epoch % 100 == 0:
                    lr = self.model.optimizer.learning_rate
                    if hasattr(lr, '__call__'):
                        print(f"\nEpoch {epoch}: Learning rate: {lr(epoch).numpy():.10f}")
                    else:
                        print(f"\nEpoch {epoch}: Learning rate: {float(lr):.10f}")

        if not silent_training:
            callbacks.append(LRLogger())

        optimizer = tf.keras.optimizers.Nadam(learning_rate=lr_schedule)
        model_instance.compile(optimizer=optimizer, loss=['mae'], metrics=['mape'])

        history = model_instance.fit(x=train_data[:, 0:3], y=train_data[:, 3],
                                     validation_data=(val_data[:, 0:3], val_data[:, 3]),
                                     epochs=nEpochs, batch_size=bSize,
                                     verbose=silent_verbose, callbacks=callbacks)

        if not silent_verbose:
            print("\n...Training completed in", len(history.history['loss']), "epochs.")

        return history

    @classmethod
    def save_model (cls, model_instance, dataset_name=None, learning_rate=None, num_epochs=None, batch_size=None,
                    norm_params=None, save_dir='saved_models'):
        """
        Save the trained model and its metadata, with automated extraction of internal attributes.

        Parameters:
        - model_instance (NNf): The trained model instance to be saved.
        - dataset_name (str): Name of the dataset used for training and validation.
        - norm_params (list): Normalization parameters used for training. Obtained from the process_data function.
        """

        os.makedirs(save_dir, exist_ok=True)

        layers_config, custom_activation_details = cls._extract_layer_details(model_instance)
        num_units_per_layer = [layer['units'] for layer in layers_config]
        num_hidden_layers = len(layers_config) - 1  # subtracting the output layer

        folder_name = cls._generate_model_folder_name(
            dataset_name=dataset_name, learning_rate=learning_rate, num_epochs=num_epochs, batch_size=batch_size,
            num_hidden_layers=num_hidden_layers, num_units_per_layer=num_units_per_layer, activation=layers_config[0]['activation'] if layers_config else 'Unknown'
        )
        model_folder_path = os.path.join(save_dir, folder_name)
        os.makedirs(model_folder_path, exist_ok=True)
        model_path = os.path.join(model_folder_path, 'model')

        # Save the model using the config
        model_instance.save(model_path)

        cls._save_metadata(model_folder_path, dataset_name, learning_rate, num_epochs, batch_size, layers_config, custom_activation_details, norm_params)

        print(f"Model saved successfully in {model_folder_path}")
        return model_folder_path

    @classmethod
    def load_model_(cls, model_dir):
        """
        Load a previously saved model along with its metadata.

        Parameters:
        - model_dir (str): Directory where the model and metadata are saved.

        Returns:
        - model (NNf): The loaded model.
        - metadata (dict): The metadata associated with the model.
        """
        metadata = cls._load_metadata(model_dir)

        norm_params = metadata.get('Normalizing Parameters', None)
        if norm_params is None:
            raise ValueError("Normalization parameters are missing in the model metadata. Cannot load the model without normalization parameters.")
        hidden_dims = [layer['units'] for layer in metadata['Layers'] if layer['units'] != 1]  # Exclude output layer

        # Extract activation function
        if metadata['Custom Activation']:
            activation_name = metadata['Custom Activation']['name']
        else:
            activation_name = metadata['Layers'][0]['activation']  # Use the activation from the first layer
        activation_func = activation_dict[activation_name]

        instance = cls(norm_params=norm_params, hidden_dims=hidden_dims, activation_func=activation_func)
        model_path = os.path.join(model_dir, 'model')

        # Load the model using the config
        loaded_model = tf.keras.models.load_model(model_path, custom_objects={'custom_act': custom_act})

        instance.set_weights(loaded_model.get_weights())

        return instance, metadata

        #%% get_config and from_config methods
    def get_config(self):
        config = super().get_config()
        config.update({
            'norm_params': self.norm_params,
            'hidden_dims': [layer.units for layer in self.hidden_layers],
            'activation_func': self.activation_func,
        })
        return config

    @classmethod
    def from_config(cls, config):
        return cls(**config)
        #%% Static Methods
    @staticmethod
    def _Normalize (u, param):
        '''Noramlize/Standardize the input (u)'''
        n_u = tf.divide(tf.math.add(u, -param[1]), param[0])
        return n_u

    @staticmethod
    def _UnNormalize (n_u, param):
        '''Un-noramlize/Un-standardize the input (n_u)'''
        u = tf.add(tf.multiply(n_u, param[0]), param[1])
        return u

    @staticmethod
    def _extract_layer_details (model_instance):
        """ Extract the configuration details of the layers, including their names, units, and activation functions.
        Also, detect and save the source code of custom activation function used in the model.
        """
        layers_config = []
        custom_activation_details = None

        for layer in model_instance.layers:
            if isinstance(layer, tf.keras.layers.Dense): # Only process Dense layers
                activation_name = None
                # Detect if the activation is custom
                for name, custom_obj in tf.keras.utils.get_custom_objects().items():
                    if hasattr(custom_obj, '__call__') and custom_obj == layer.activation:
                        activation_name = name
                        # Save source code of the custom activation function
                        if custom_activation_details is None:   # Only save the first custom activation found
                            custom_activation_details = {
                                "name": name,
                                "source_code": inspect.getsource(custom_act)
                            }
                        break

                if not activation_name:     # Handle standard activations
                    activation_name = layer.activation.__name__ if hasattr(layer.activation, '__name__') else 'unknown'
                # Store layer details
                layer_info = {
                    "name": layer.name,
                    "units": layer.units,
                    "activation": activation_name
                }
                layers_config.append(layer_info)

        return layers_config, custom_activation_details

    @staticmethod
    def _generate_model_folder_name (dataset_name, learning_rate, num_epochs, batch_size, num_hidden_layers, num_units_per_layer, activation):
        # Convert num_units_per_layer to the desired format
        num_units_str = "_".join(map(str, num_units_per_layer))
        num_units_formatted = f"({num_units_str})"

        model_attributes = {
            'D': dataset_name or 'Unknown',                    # Dataset name
            'LR': learning_rate or 'Unknown',                   # Learning rate
            'BS': batch_size or 'Unknown',                      # Batch size
            'E': num_epochs or 'Unknown',                       # Number of epochs
            'HL': num_hidden_layers,                            # Number of Hidden Layers
            'N': num_units_formatted,                           # Number of units
            'ACT': activation or 'Unknown',                     # Activation functions
        }
        folder_name = "_".join([f"{key}{value}" for key, value in model_attributes.items()])
        folder_name = f'{folder_name}_{datetime.now().strftime("%Y-%m-%d_%H-%M-%S")}'
        return folder_name

    @staticmethod
    def _save_metadata (model_folder_path, dataset_name, learning_rate, num_epochs, batch_size, layers_config, custom_activation_details, norm_params):
        # Convert norm_params to a serializable format
        norm_params = norm_params.tolist() if isinstance(norm_params, np.ndarray) else norm_params
        model_metadata = {
            'Dataset name': dataset_name or 'Unknown',          # Name of the dataset used for training and validation.
            'Learning Rate': learning_rate or 'Unknown',
            'Batch Size': batch_size or 'Unknown',
            'Num Epochs': num_epochs or 'Unknown',
            'Layers': layers_config,                            # List of dictionaries for each layer, including name, units, and activation
            'Normalizing Parameters': norm_params or None,
            'Custom Activation': custom_activation_details      # Save custom activation function details (if any)
        }

        metadata_path = os.path.join(model_folder_path, 'metadata.json')
        with open(metadata_path, 'w') as metadata_file:
            json.dump(model_metadata, metadata_file, indent=4)

    @staticmethod
    def _load_metadata (model_dir):
        metadata_path = os.path.join(model_dir, 'metadata.json')
        with open(metadata_path, 'r') as metadata_file:
            metadata = json.load(metadata_file)

        return metadata

#%% ---- NNz Class ----
class NNz (tf.keras.Model):
    def __init__ (self, norm_params, hidden_dims, activation_func=activation_dict['leaky_relu']):
        super().__init__()
        self.num_inputs = 3
        self.num_outputs = 1
        self.activation_func = activation_func
        self.norm_params = norm_params

        self.hidden_layers =[]
        for i, h_dim in enumerate(hidden_dims):
            self.hidden_layers.append(tf.keras.layers.Dense(
                h_dim,
                activation=self.activation_func,
                name=f'NNz_Hlayer{i+1}',
            ))
        self.out_layer = tf.keras.layers.Dense(self.num_outputs, name='NNz_OutLayer')

        self.build((None, self.num_inputs))

    def call (self, inputs):
        inp_Dε = tf.slice(inputs, [0, 0], [-1, 1])
        inp_σ_t = tf.slice(inputs, [0, 1], [-1, 1])
        # inp_ε_t = tf.slice(inputs, [0, 2], [-1, 1])   # Not used in NNz
        inp_ζ_t = tf.slice(inputs, [0, 2], [-1, 1])
        n_concat = tf.concat([inp_Dε, inp_σ_t, inp_ζ_t], axis=1, name='inputs_concat_of_NNz')

        for layer in self.hidden_layers:
            n_concat = layer(n_concat)
        n_Dζ = self.out_layer(n_concat)

        return n_Dζ

    def infer (self, input_test):
        Dε_full = tf.slice(input_test, [0, 0], [-1, 1])
        σ_t_full = tf.slice(input_test, [0, 1], [-1, 1])
        # ε_t_full = tf.slice(input_test, [0, 5], [-1, 1])                  # un-comment if ε_t is used as input
        ζ_t_full = tf.slice(input_test, [0, 7], [-1, 1])
        # Normalize inputs
        n_Dε_full = self._Normalize(Dε_full, self.norm_params[0])
        n_σ_t_full = self._Normalize(σ_t_full, self.norm_params[1])
        # n_ε_t_full = self._Normalize(ε_t_full, self.norm_params[5])
        n_ζ_t_full = self._Normalize(ζ_t_full, self.norm_params[7])

        n_ζ_t = n_ζ_t_full[0, None]
        del ζ_t_full, n_ζ_t_full        # To make sure they are not used later in the method.
        num_test = len(n_Dε_full)

        pred_Dζ, Dζ_errors, Dζ_errors_percent = \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64), \
            np.empty((num_test, 1), dtype=np.float64)

        step_results = []

        for i in range(num_test):
            input_i = tf.concat([n_Dε_full[i, None], n_σ_t_full[i, None], n_ζ_t], axis=1)
            n_Dζ_pred = self.call(input_i)
            Dζ_pred = self._UnNormalize(n_Dζ_pred, self.norm_params[8]).numpy()[0, 0]
            pred_Dζ[i, 0] = Dζ_pred

            # Calculate errors if true values are available (input has at least 7 columns)
            if input_test.shape[1] >= 9:
                true_Dζ = input_test[i, 8]

                Dζ_error = Dζ_pred - true_Dζ
                Dζ_error_percent = (Dζ_error / true_Dζ) * 100 if true_Dζ != 0 else float('inf')

                Dζ_errors[i, 0] = Dζ_error
                Dζ_errors_percent[i, 0] = Dζ_error_percent
            else:
                true_Dζ = None
                Dζ_errors[i, 0] = None
                Dζ_errors_percent[i, 0] = None

            step_result = {
                'step': i,
                'inputs': {
                    'Dε': Dε_full.numpy()[i, 0],
                    'σ_t': σ_t_full.numpy()[i, 0],
                    # 'ε_t': ε_t_full.numpy()[i, 0],            # un-comment if ε_t is used as input
                    'ζ_t': self._UnNormalize(n_ζ_t, self.norm_params[7]).numpy()[0, 0]
                },
                'predictions': {'Dζ': Dζ_pred}
            }

            # Add true values and errors if available
            if input_test.shape[1] >= 9:
                step_result['true_values'] = {'Dζ': true_Dζ}
                step_result['errors'] = {'Dζ': Dζ_error, 'Dζ_percent': Dζ_error_percent}

            step_results.append(step_result)

            # Update ζ_t for the next step
            if i < num_test - 1:
                ζ_t = self._UnNormalize(n_ζ_t, self.norm_params[7])
                ζ_t = tf.add(ζ_t, Dζ_pred)
                n_ζ_t = self._Normalize(ζ_t, self.norm_params[7])

        summary = {'pred_Dζ': pred_Dζ.flatten().tolist()}

        if input_test.shape[1] >= 9:
            summary.update({
                'true_Dζ': input_test[:, 8].tolist(),
                'Dζ_errors': Dζ_errors.flatten().tolist(),
                'Dζ_errors_percent': Dζ_errors_percent.flatten().tolist(),
                'avg_Dζ_error': float(np.mean(np.abs(Dζ_errors))),
                'avg_Dζ_error_percent': float(np.mean(np.abs(Dζ_errors_percent))),
                'max_Dζ_error': float(np.max(np.abs(Dζ_errors))),
                'max_Dζ_error_percent': float(np.max(np.abs(Dζ_errors_percent)))
            })

        result_dict = {
            'step_results': step_results,
            'summary': summary,
            'num_samples': num_test
        }

        return result_dict

    @staticmethod
    def _weighted_mae_loss(y_true, y_pred):
        """Custom weighted MAE loss function that assigns higher weights to non-zero targets"""
        base_weight = 4.0  # Weight for zero targets (majority class)
        non_zero_weight = 3.0  # Weight for non-zero targets (minority class)

        # Create weight tensor based on whether targets are non-zero
        weights = tf.where(tf.not_equal(y_true, 0), non_zero_weight, base_weight)

        # Calculate absolute errors and apply weights
        abs_errors = tf.cast(tf.abs(tf.subtract(y_true, y_pred)), GLOBAL_DTYPE)
        weights = tf.cast(weights, GLOBAL_DTYPE)
        weighted_errors = abs_errors * weights

        return tf.reduce_mean(weighted_errors)

    @staticmethod
    def _physics_informed_loss(y_true, y_pred):
        """
        Physics-informed loss function that combines weighted MAE with a penalty for negative predictions.
        This loss function enforces the physical constraint that Δζ should never be negative.

        Parameters:
        - y_true: True values
        - y_pred: Predicted values

        Returns:
        - total_loss: Combined loss value
        """
        # Get the base weighted MAE loss
        base_loss = NNz._weighted_mae_loss(y_true, y_pred)

        # Physics-based penalty for negative predictions (quadratic penalty)
        negative_penalty = tf.reduce_mean(tf.square(tf.minimum(y_pred, 0)))

        lambda_penalty = 0.8  # Penalty weight for negative predictions
        total_loss = base_loss + lambda_penalty * negative_penalty

        return total_loss

    @staticmethod
    def _base_loss_metric(y_true, y_pred):
        """Metric to monitor the base weighted MAE loss component"""
        return NNz._weighted_mae_loss(y_true, y_pred)

    @staticmethod
    def _negative_penalty_metric(y_true, y_pred):
        """Metric to monitor the negative penalty component"""
        return tf.reduce_mean(tf.square(tf.minimum(y_pred, 0)))

    @classmethod
    def train_model (cls, model_instance, train_data, val_data, LearningRate, nEpochs, bSize, silent_training=False,
                     early_stopping=None, lr_schedule_type='exponential', loss_type='weighted_mae'):
        """
        Train the model with learning rate scheduling and optional physics-informed loss.

        Parameters:
        - model_instance: Instance of NNz to train
        - train_data: Training data with shape (num_samples, num_features)
        - val_data: Validation data with shape (num_samples, num_features)
        - LearningRate: Initial learning rate for the optimizer
        - nEpochs: Number of epochs to train
        - bSize: Batch size
        - silent_training: If True, suppress training output
        - early_stopping: Early stopping callback
        - lr_schedule_type: Type of learning rate schedule ('exponential', 'cosine', or 'constant')
        - loss_type: Type of loss function to use ('weighted_mae' or 'physics_informed')

        Returns:
        - history: Training history
        """
        silent_verbose = 0 if silent_training else 1
        if early_stopping is None:
            early_stopping = tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=500, restore_best_weights=True)

        callbacks = [early_stopping]

        if lr_schedule_type == 'exponential':
            # Exponential decay: decay to 1% of initial LR over the training period
            decay_steps = nEpochs // 2  # Decay significantly over 1/2 of training
            decay_rate = 0.85   # Decay the learning rate every decay_steps
            lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
                initial_learning_rate=LearningRate,
                decay_steps=decay_steps,
                decay_rate=decay_rate,
                staircase=True  # Smooth decay
            )
            print(f"Using exponential learning rate decay: initial={LearningRate}, decay_steps={decay_steps}, decay_rate={decay_rate}")

        elif lr_schedule_type == 'cosine':
            # Cosine decay with restarts
            first_decay_steps = nEpochs // 3  # Increased from nEpochs//5 to nEpochs//3
            lr_schedule = tf.keras.optimizers.schedules.CosineDecayRestarts(
                initial_learning_rate=LearningRate,  # Reduced initial learning rate (0.001 if LearningRate was 0.0012)
                first_decay_steps=first_decay_steps,
                t_mul=2.0,  # Each cycle gets longer
                m_mul=0.95,  # Increased from 0.9 to 0.95 for gentler learning rate reduction
                alpha=1e-5   # Minimum learning rate
            )
            print(f"Using cosine decay with restarts: initial={LearningRate}, first_decay_steps={first_decay_steps}")

        else:  # 'constant' or any other value
            lr_schedule = LearningRate
            print(f"Using constant learning rate: {LearningRate}")

        # Add LearningRateScheduler callback to log the learning rate
        class LRLogger(tf.keras.callbacks.Callback):
            def on_epoch_end(self, epoch, logs=None):
                # logs parameter is required by TensorFlow's callback API but not used here
                if not silent_training and epoch % 100 == 0:
                    lr = self.model.optimizer.learning_rate
                    if hasattr(lr, '__call__'):
                        print(f"\nEpoch {epoch}: Learning rate: {lr(epoch).numpy():.10f}")
                    else:
                        print(f"\nEpoch {epoch}: Learning rate: {float(lr):.10f}")

        if not silent_training:
            callbacks.append(LRLogger())

        optimizer = tf.keras.optimizers.Nadam(learning_rate=lr_schedule)

        # Select loss function based on loss_type
        if loss_type.lower() == 'physics_informed':
            loss_fn = cls._physics_informed_loss
            metrics = ['mape', cls._base_loss_metric, cls._negative_penalty_metric]
            print(f"Using physics-informed loss function with negative penalty")
        else:  # Default to weighted_mae
            loss_fn = cls._weighted_mae_loss
            metrics = ['mape']
            print(f"Using weighted MAE loss function")

        model_instance.compile(optimizer=optimizer, loss=loss_fn, metrics=metrics)
        # model_instance.compile(optimizer=optimizer, loss=['mae'], metrics=['mape'])

        history = model_instance.fit(x=train_data[:,[0, 1, 7]], y=train_data[:, 8],
                                     validation_data=(val_data[:, [0, 1, 7]], val_data[:, 8]),
                                     epochs=nEpochs, batch_size=bSize,
                                     verbose=silent_verbose, callbacks=callbacks)

        if not silent_verbose:
            print("\n...Training completed in", len(history.history['loss']), "epochs.")

        return history

    @classmethod
    def save_model (cls, model_instance, dataset_name=None, learning_rate=None, LR_schedule_type=None, num_epochs=None, batch_size=None,
                    norm_params=None, loss_type=None, save_dir='saved_models/NNz'):
        """
        Save the trained NNz network and its metadata, with automated extraction of internal attributes.

        Parameters:
        - model_instance (NNz): The trained model instance to be saved.
        - dataset_name (str): Name of the dataset used for training and validation.
        - norm_params (list): Normalization parameters used for training. Obtained from the "process_data" function.
        """

        os.makedirs(save_dir, exist_ok=True)
        layers_config, custom_activation_details = cls._extract_layer_details(model_instance)
        num_units_per_layer = [layer['units'] for layer in layers_config]
        num_hidden_layers = len(layers_config) - 1  # subtracting the output layer

        folder_name = cls._generate_model_folder_name(
            dataset_name=dataset_name, learning_rate=learning_rate, LR_schedule_type=LR_schedule_type, num_epochs=num_epochs, batch_size=batch_size,
            num_hidden_layers=num_hidden_layers, num_units_per_layer=num_units_per_layer, activation=layers_config[0]['activation'] if layers_config else 'Unknown',
            loss_type=loss_type
        )
        model_folder_path = os.path.join(save_dir, folder_name)
        os.makedirs(model_folder_path, exist_ok=True)
        model_path = os.path.join(model_folder_path, 'model')

        # Save the model using the config
        model_instance.save(model_path)

        cls._save_metadata(model_folder_path=model_folder_path, dataset_name=dataset_name, learning_rate=learning_rate, LR_schedule_type=LR_schedule_type,
                           num_epochs=num_epochs, batch_size=batch_size, layers_config=layers_config, custom_activation_details=custom_activation_details,
                           norm_params=norm_params, loss_type=loss_type)

        print(f"Model saved successfully in {model_folder_path}")
        return model_folder_path

    @classmethod
    def load_model_(cls, model_dir):
        """
        Load a previously saved model along with its metadata.

        Parameters:
        - model_dir (str): Directory where the model and metadata are saved.

        Returns:
        - model (NNz): The loaded model.
        - metadata (dict): The metadata associated with the model.
        """

        metadata = cls._load_metadata(model_dir)

        norm_params = metadata.get('Normalizing Parameters', None)
        if norm_params is None:
            raise ValueError("Normalization parameters are missing in the model metadata. Cannot load the model without normalization parameters.")
        hidden_dims = [layer['units'] for layer in metadata['Layers'] if layer['units'] != 1]  # Exclude output layer

        # Extract activation function if any
        if metadata['Custom Activation']:
            activation_name = metadata['Custom Activation']['name']
        else:
            activation_name = metadata['Layers'][0]['activation']  # Use the activation from the first layer

        if activation_name not in activation_dict:
            raise ValueError(f"Activation function '{activation_name}' is not supported.")
        activation_func = activation_dict[activation_name]

        instance = cls(norm_params=norm_params, hidden_dims=hidden_dims, activation_func=activation_func)
        model_path = os.path.join(model_dir, 'model')

        # Load the model using the config
        loaded_model = tf.keras.models.load_model(model_path, custom_objects={'custom_act': custom_act})
        instance.set_weights(loaded_model.get_weights())

        return instance, metadata

        #%% get_config and from_config methods
    def get_config(self):
        config = super().get_config()
        config.update({
            'norm_params': self.norm_params,
            'hidden_dims': [layer.units for layer in self.hidden_layers],
            'activation_func': self.activation_func,
        })
        return config

    @classmethod
    def from_config(cls, config):
        return cls(**config)
    #%% Static Methods
    @staticmethod
    def _Normalize (u, param):
            '''Noramlize/Standardize the input (u)'''
            n_u = tf.divide(tf.math.add(u, -param[1]), param[0])
            return n_u

    @staticmethod
    def _UnNormalize (n_u, param):
            '''Un-noramlize/Un-standardize the input (n_u)'''
            u = tf.add(tf.multiply(n_u, param[0]), param[1])
            return u

    @staticmethod
    def _extract_layer_details (model_instance):
        """ Extract the configuration details of the layers, including their names, units, and activation functions.
        Also, detect and save the source code of custom activation function used in the model.
        """
        layers_config = []
        custom_activation_details = None

        for layer in model_instance.layers:
            if isinstance(layer, tf.keras.layers.Dense): # Only process Dense layers
                activation_name = None
                # Detect if the activation is custom
                for name, custom_obj in tf.keras.utils.get_custom_objects().items():
                    if hasattr(custom_obj, '__call__') and custom_obj == layer.activation:
                        activation_name = name
                        # Save source code of the custom activation function
                        if custom_activation_details is None:   # Only save the first custom activation found
                            custom_activation_details = {
                                "name": name,
                                "source_code": inspect.getsource(custom_act)
                            }
                        break

                if not activation_name:     # Handle standard activations
                    # Check if it's a LeakyReLU layer
                    if isinstance(layer.activation, tf.keras.layers.LeakyReLU):
                        activation_name = 'leaky_relu'
                    else:
                        activation_name = layer.activation.__name__ if hasattr(layer.activation, '__name__') else 'unknown'
                # Store layer details
                layer_info = {
                    "name": layer.name,
                    "units": layer.units,
                    "activation": activation_name
                }
                layers_config.append(layer_info)

        return layers_config, custom_activation_details

    @staticmethod
    def _generate_model_folder_name (dataset_name, learning_rate, LR_schedule_type, num_epochs, batch_size, num_hidden_layers, num_units_per_layer, activation, loss_type=None):
        # Convert num_units_per_layer to the desired format
        num_units_str = "_".join(map(str, num_units_per_layer))
        num_units_formatted = f"({num_units_str})"

        # Shorten loss type for folder name
        loss_abbr = 'PI' if loss_type and loss_type.lower() == 'physics_informed' else 'WM'

        model_attributes = {
            'D': dataset_name or 'Unknown',                     # Dataset name
            'LR': learning_rate or 'Unknown',                   # Learning rate
            'Schedule': LR_schedule_type or 'Unknown',          # Learning rate schedule type
            'BS': batch_size or 'Unknown',                      # Batch size
            'E': num_epochs or 'Unknown',                       # Number of epochs
            'HL': num_hidden_layers,                            # Number of Hidden Layers
            'N': num_units_formatted,                           # Number of units
            'ACT': activation or 'Unknown',                     # Activation functions
            'Loss': loss_abbr,                                  # Loss function type
        }
        folder_name = "_".join([f"{key}{value}" for key, value in model_attributes.items()])
        folder_name = f'{folder_name}_{datetime.now().strftime("%Y-%m-%d_%H-%M-%S")}'
        return folder_name

    @staticmethod
    def _save_metadata (model_folder_path, dataset_name, learning_rate, LR_schedule_type, num_epochs, batch_size, layers_config, custom_activation_details, norm_params, loss_type=None):
        # Convert norm_params to a serializable format
        norm_params = norm_params.tolist() if isinstance(norm_params, np.ndarray) else norm_params
        model_metadata = {
            'Dataset name': dataset_name or 'Unknown',          # Name of the dataset used for training and validation.
            'Learning Rate': learning_rate or 'Unknown',
            'Learning Rate Schedule Type': LR_schedule_type or 'Unknown',
            'Batch Size': batch_size or 'Unknown',
            'Num Epochs': num_epochs or 'Unknown',
            'Loss Type': loss_type or 'weighted_mae',           # Type of loss function used for training
            'Layers': layers_config,                            # List of dictionaries for each layer, including name, units, and activation
            'Normalizing Parameters': norm_params or None,
            'Custom Activation': custom_activation_details      # Save custom activation function details (if any)
        }

        metadata_path = os.path.join(model_folder_path, 'metadata.json')
        with open(metadata_path, 'w') as metadata_file:
            json.dump(model_metadata, metadata_file, indent=4)

    @staticmethod
    def _load_metadata (model_dir):
        metadata_path = os.path.join(model_dir, 'metadata.json')
        with open(metadata_path, 'r') as metadata_file:
            metadata = json.load(metadata_file)

        return metadata

#%% ---- NNz + Classifier Class ----
class NNzWithClassifier (NNz):
    def __init__ (self, norm_params, hidden_dims, activation_func=activation_dict['leaky_relu'], classifier=None, classifier_method='hybrid'):

        super().__init__(norm_params, hidden_dims, activation_func)
        self.classifier = classifier
        self.classifier_method = classifier_method

    def infer (self, input_test):
        """
        Modified infer method that incorporates binary classifier within each iteration.
        """
        # Extract and normalize inputs
        Dε_full = tf.slice(input_test, [0, 0], [-1, 1])
        σ_t_full = tf.slice(input_test, [0, 1], [-1, 1])
        ζ_t_full = tf.slice(input_test, [0, 7], [-1, 1])

        n_Dε_full = self._Normalize(Dε_full, self.norm_params[0])
        n_σ_t_full = self._Normalize(σ_t_full, self.norm_params[1])
        n_ζ_t_full = self._Normalize(ζ_t_full, self.norm_params[7])

        n_ζ_t = n_ζ_t_full[0, None]
        del ζ_t_full, n_ζ_t_full
        num_test = len(n_Dε_full)

        # Verify classifier input shape if available
        if self.classifier is not None and hasattr(self.classifier, 'model') and hasattr(self.classifier.model, 'input_shape'):
            input_shape = self.classifier.model.input_shape
            if input_shape and len(input_shape) > 1:
                expected_features = input_shape[1]
                if expected_features != 3:
                    raise ValueError(f"Classifier expects {expected_features} features, but exactly 3 features are required. Please retrain the classifier with the correct input shape.")

        # Initialize arrays for predictions and errors
        pred_Dζ = np.empty((num_test, 1), dtype=np.float64)
        Dζ_errors = np.empty((num_test, 1), dtype=np.float64)
        Dζ_errors_percent = np.empty((num_test, 1), dtype=np.float64)
        step_results = []

        for i in range(num_test):
            input_i = tf.concat([n_Dε_full[i, None], n_σ_t_full[i, None], n_ζ_t], axis=1)
            n_Dζ_pred = self.call(input_i)
            Dζ_pred_raw = self._UnNormalize(n_Dζ_pred, self.norm_params[8]).numpy()[0, 0]

            # Apply binary classifier if available
            if self.classifier is not None:
                try:
                    # Prepare data for binary classifier (exactly 3 features)
                    classifier_input = np.array([[
                        Dε_full.numpy()[i, 0],
                        σ_t_full.numpy()[i, 0],
                        self._UnNormalize(n_ζ_t, self.norm_params[7]).numpy()[0, 0]
                    ]])

                    binary_pred, binary_prob = self.classifier.predict(classifier_input)

                    if self.classifier_method == 'hard':
                        # Hard threshold (binary decision)
                        if binary_pred[0] == 0:  # If classifier predicts zero
                            Dζ_pred = 0.0
                        else:
                            Dζ_pred = Dζ_pred_raw

                    elif self.classifier_method == 'soft':
                        # Soft threshold (confidence-weighted)
                        # binary_prob[0][0] is the probability of being non-zero
                        Dζ_pred = Dζ_pred_raw * binary_prob[0][0]

                    elif self.classifier_method == 'adaptive':
                        # Adaptive threshold (based on prediction magnitude)
                        if binary_prob[0][0] < 0.3 or abs(Dζ_pred_raw) < 1e-6:
                            Dζ_pred = 0.0
                        else:
                            Dζ_pred = Dζ_pred_raw

                    elif self.classifier_method == 'hybrid':
                        # Hybrid approach (combine confidence and magnitude)
                        zero_confidence = 1 - binary_prob[0][0]  # Confidence it's zero
                        magnitude_factor = np.tanh(abs(Dζ_pred_raw) * 1e5)  # Scale based on magnitude

                        if zero_confidence > 0.7 or (zero_confidence > 0.5 and magnitude_factor < 0.3):
                            Dζ_pred = 0.0
                        else:
                            Dζ_pred = Dζ_pred_raw

                    else:
                        Dζ_pred = Dζ_pred_raw
                except Exception as e:
                    print(f"Error applying classifier at step {i}: {e}")
                    Dζ_pred = Dζ_pred_raw  # Fall back to raw prediction
            else:
                Dζ_pred = Dζ_pred_raw

            # Store prediction
            pred_Dζ[i, 0] = Dζ_pred

            # Calculate errors if true values are available
            if input_test.shape[1] >= 9:
                true_Dζ = input_test[i, 8]
                Dζ_error = Dζ_pred - true_Dζ
                Dζ_error_percent = (Dζ_error / true_Dζ) * 100 if true_Dζ != 0 else float('inf')
                Dζ_errors[i, 0] = Dζ_error
                Dζ_errors_percent[i, 0] = Dζ_error_percent
            else:
                true_Dζ = None
                Dζ_errors[i, 0] = None
                Dζ_errors_percent[i, 0] = None

            # Store step result
            step_result = {
                'step': i,
                'inputs': {
                    'Dε': Dε_full.numpy()[i, 0],
                    'σ_t': σ_t_full.numpy()[i, 0],
                    'ζ_t': self._UnNormalize(n_ζ_t, self.norm_params[7]).numpy()[0, 0]
                },
                'predictions': {'Dζ': Dζ_pred}
            }

            if input_test.shape[1] >= 9:
                step_result['true_values'] = {'Dζ': true_Dζ}
                step_result['errors'] = {'Dζ': Dζ_error, 'Dζ_percent': Dζ_error_percent}

            step_results.append(step_result)

            # Update ζ_t for the next step using the classifier-corrected prediction
            if i < num_test - 1:
                ζ_t = self._UnNormalize(n_ζ_t, self.norm_params[7])
                ζ_t = tf.add(ζ_t, Dζ_pred)  # Use the classifier-corrected prediction
                n_ζ_t = self._Normalize(ζ_t, self.norm_params[7])

        # Prepare result dictionary
        summary = {'pred_Dζ': pred_Dζ.flatten().tolist()}

        if input_test.shape[1] >= 9:
            summary.update({
                'true_Dζ': input_test[:, 8].tolist(),
                'Dζ_errors': Dζ_errors.flatten().tolist(),
                'Dζ_errors_percent': Dζ_errors_percent.flatten().tolist(),
                'avg_Dζ_error': float(np.mean(np.abs(Dζ_errors))),
                'avg_Dζ_error_percent': float(np.mean(np.abs(Dζ_errors_percent))),
                'max_Dζ_error': float(np.max(np.abs(Dζ_errors))),
                'max_Dζ_error_percent': float(np.max(np.abs(Dζ_errors_percent)))
            })

        result_dict = {
            'step_results': step_results,
            'summary': summary,
            'num_samples': num_test
        }

        return result_dict
